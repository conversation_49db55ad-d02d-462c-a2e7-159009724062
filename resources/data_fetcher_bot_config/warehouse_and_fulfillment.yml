agent_name: warehouse_and_fulfillment
model_provider: xm
model: kimi-k2-turbo
tools:
  - name: fetch_mysql_sql_result
  - name: get_table_sample_data
  - name: fetch_ddl_for_table
  - name: search_product_by_name
  - name: query_order_item_trace_info
  - name: query_product_warehouse_inventory
  - name: query_warehouse_in_transit_inventory
  - name: query_product_supply_warehouse
  - name: query_product_certificate_reports
# 配置在哪些工具执行后停止，允许用户查看结果
stop_at_tool_names:
  - fetch_mysql_sql_result
need_system_prompt: false
agent_description: warehouse_and_fulfillment.md
agent_tables:
  - name: warehouse_storage_center
  - name: area_store
  - name: orders
  - name: order_item
  - name: inventory
  - name: products
  - name: delivery_plan
  - name: contact
    desc: 联系人收货地址表，存储商户的收货地址信息，每个contact_id代表一个配送点位。关联查询`merchant`.`m_id`，包含联系人信息、详细地址、配送仓库编号(store_no)、距离仓库距离(distance)等
  - name: supplier
  - name: warehouse_batch_prove_record
  - name: fence
  - name: warehouse_inventory_mapping
  - name: area
    desc: 运营服务区的基本信息，包括运营服务区编码(area_no)、运营服务区名字、运营服务区所属的大区编码(large_area_no)等
  - name: purchase_product_warehouse_config
    desc: 商品仓库采购配置表，记录每个商品在各个仓库的采购负责人及库存管理配置信息，包括商品SPU编号(pd_id，关联products.pd_id)、仓库编号(warehouse_no，关联warehouse_storage_center.warehouse_no)、采购类型(purchase_type：1-直采，2-非直采)、采购负责人ID(admin_id)、采购负责人姓名(admin_name)、安全库存水位(safe_water_level)、备货天数(backlog_day)等
  - name: category
    desc: 商品后端类目表，记录商品的类目信息，包括类目ID(id)、类目名字(category)、类目类型(type，=4表示水果类目)等
agent_as_tool_description: |
  这是一个专门用于仓储物流全链路管理的AI机器人，覆盖从商品入库到配送履约的完整供应链流程。

  **核心仓储管理能力：**
  - 实时库存查询：支持按仓库、SKU、运营服务区等维度查询可售库存、锁定库存状态
  - 在途库存管理：分别统计采购在途和调拨在途商品，包括预计到货日期、到货数量等关键信息
  - 仓库路由分析：通过运营服务区、围栏、仓库映射关系自动识别商品供货仓库
  - 滞销品检测：识别库存积压超过30天且库存量大于100的商品

  **订单履约全流程跟踪：**
  - 订单配送计划：跟踪从订单创建到配送完成的全流程状态，支持省心送和普通订单的差异化处理
  - 配送金额计算：区分不同订单类型计算配送金额，支持按日期查询配送计划详情
  - 订单溯源查询：支持查询订单商品的批次号、供应商信息、配送照片，实现完整的商品溯源和配送照片

  **商品信息管理：**
  - 多规格商品管理：支持SPU和SKU两级商品体系，处理同一商品的不同规格
  - 四证查询系统：质检报告、报关证明、核酸检测、消毒证明等证件的查询和管理
  - 证件报告处理：自动处理质检报告链接，确保多条报告地址的完整URL转换
  - 商品属性管理：支持品牌、规格、产地等商品属性的查询和分析

  **采购与入库管理：**
  - 采购计划跟踪：从采购计划生成到供应商确认的全流程管理
  - 入库预约管理：通过入库预约单跟踪商品预计到货时间和入库状态
  - 采购配置管理：记录每个商品在各仓库的采购负责人、采购类型、安全库存水位、备货天数等配置
  - 供应商管理：维护供应商信息，支持批次与供应商的关联查询

  **调拨与库存优化：**
  - 调拨单管理：跟踪仓库间商品调拨的全流程，包括调拨任务和预计入库时间
  - 库存平衡：通过调拨系统实现不同仓库间的库存平衡和优化配置
  - 调拨状态跟踪：实时监控调拨单的执行状态和完成情况

  **数据分析与报表：**
  - 库存周转分析：分析商品在各仓库的库存周转情况和库存健康度
  - 仓库效率分析：按仓库维度分析出库效率、配送及时率等关键指标

  **特殊业务场景支持：**
  - 鲜果商品管理：针对鲜果类商品的特殊库存管理和配送要求
  - 标品管理：除鲜果外的其他商品的标准化管理流程
  - 测试环境过滤：查询时自动排除测试仓库，确保数据的真实性和准确性

  **技术特点：**
  - 严格遵循数据真实性原则，不允许对数据进行假设编造
  - 支持复杂的多表关联查询，涵盖23个核心业务表
  - 自动处理仓库路由逻辑，当用户未指定仓库时自动通过区域信息定位仓库
  - 提供完整的商品溯源链条，从原材料到最终配送的全程可追溯

  该机器人适用于生鲜电商、供应链管理等场景，为仓储运营、商品采购、库存管理等提供全方位的数据支持和业务分析。
