-- 【注意】此表是ODPS表，仅可使用ODPS SQL来获取数据，无法使用MySQL来查询。
-- 需要使用工具：fetch_odps_sql_result 来查询。
-- 使用时，必须要加上过滤条件ds=max_pt('app_chatbi_supplier_order_analysis_df') 来获取最新数据，其他ds都是非法的，不加ds条件会报错。

CREATE TABLE IF NOT EXISTS app_chatbi_supplier_order_analysis_df (
    order_no STRING COMMENT '订单号',
    tenant_id BIGINT COMMENT '租户ID，用于多租户数据隔离，标识数据所属的租户, tenant_id = 1说明是鲜沐或者顺鹿达的数据',
    warehouse_no BIGINT COMMENT '仓库编号，标识商品所在的具体仓库，用于仓库维度分析',
    warehouse_name STRING COMMENT '仓库名称，比如：嘉兴总仓、南京总仓等',
    sku STRING COMMENT 'SKU商品编码',
    batch STRING COMMENT '批次号，商品的批次信息，用于追溯商品来源和质量管理',
    order_time DATETIME COMMENT '订单创建时间。比如：2025-07-17 14:35:27',
    sku_price DECIMAL(38,18) COMMENT 'SKU单价，商品的单位价格，单位为元，保留2位小数',
    sku_subtotal_price DECIMAL(38,18) COMMENT 'SKU小计金额，等于数量乘以单价，单位为元，用于计算订单总价值',
    quantity BIGINT COMMENT 'SKU数量，该SKU在订单中的出库数量，支持大数值存储',
    sku_sub_type BIGINT COMMENT 'SKU子类型，枚举：1:代销不入仓(俗称‘全品类’)、2:代销入仓(俗称‘全品类’)、3:自营(经销)(我司自行采购、入库、销售)、5:顺鹿达商品(鲜果POP)',
    pd_name STRING COMMENT '商品名称，商品的中文名称描述，比如：安佳淡奶油、越南大青芒等',
    weight STRING COMMENT 'SKU规格，比如1*12L之类的',
    supplier_id STRING COMMENT '供应商ID，比如：1538',
    supplier_name STRING COMMENT '供应商名称，供应商的企业名称，比如：上海乐厨食品有限公司',
    supplier_manager STRING COMMENT '该供应商的负责人姓名，也就是我司的采购员名字'
)
COMMENT '供应商订单分析结果表，整合订单、商品、仓库、供应商等维度信息，重点支持供应商维度的分析，比如供应商的销售情况等'
PARTITIONED BY 
(
    ds                         STRING COMMENT '分区日期'
)