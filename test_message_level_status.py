#!/usr/bin/env python3
"""
测试消息级别的 good/bad case 状态显示是否正确
"""

import requests
import json

def test_message_level_status():
    """测试消息级别状态显示"""
    
    print("🔍 测试消息级别的 good/bad case 状态显示...")
    
    try:
        # 获取一些 conversation 数据
        url = 'http://127.0.0.1:5700/api/dashboard/logs?limit=5'
        print(f"📡 请求 URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ API 请求失败: {response.status_code}")
            return False
            
        data = response.json()
        conversations = data.get('conversations', {})
        
        if not conversations:
            print("❌ 没有找到 conversation 数据")
            return False
            
        print(f"✅ 成功获取 {len(conversations)} 个 conversation")
        
        # 检查每个 conversation 的消息级别状态
        for conv_id, conv_data in conversations.items():
            print(f"\n📋 Conversation: {conv_id}")
            print(f"   Title: {conv_data.get('title', 'N/A')}")
            
            # 检查 conversation 级别状态
            conv_has_good = conv_data.get('hasGoodCase', False)
            conv_has_bad = conv_data.get('hasBadCase', False)
            
            print(f"   📊 Conversation 级别状态:")
            print(f"      hasGoodCase: {conv_has_good}")
            print(f"      hasBadCase: {conv_has_bad}")
            
            # 检查消息级别状态
            messages = conv_data.get('messages', [])
            print(f"   📝 Messages ({len(messages)} 条):")
            
            msg_good_count = 0
            msg_bad_count = 0
            
            for i, msg in enumerate(messages):
                msg_good = msg.get('is_good_case', False)
                msg_bad = msg.get('is_bad_case', False)
                
                if msg_good:
                    msg_good_count += 1
                if msg_bad:
                    msg_bad_count += 1
                    
                if msg_good or msg_bad:
                    print(f"      Message {i+1} ({msg.get('role', 'unknown')}): good={msg_good}, bad={msg_bad}")
            
            print(f"   📈 统计:")
            print(f"      Good case messages: {msg_good_count}")
            print(f"      Bad case messages: {msg_bad_count}")
            
            # 验证 conversation 级别状态是否正确反映消息级别状态
            expected_has_good = msg_good_count > 0
            expected_has_bad = msg_bad_count > 0
            
            if conv_has_good == expected_has_good and conv_has_bad == expected_has_bad:
                print(f"   ✅ Conversation 级别状态计算正确")
            else:
                print(f"   ❌ Conversation 级别状态计算错误:")
                print(f"      期望: hasGoodCase={expected_has_good}, hasBadCase={expected_has_bad}")
                print(f"      实际: hasGoodCase={conv_has_good}, hasBadCase={conv_has_bad}")
        
        print(f"\n🎉 测试完成！")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试消息级别状态显示...")
    
    success = test_message_level_status()
    
    if success:
        print("\n🎉 测试通过！消息级别状态显示正确。")
    else:
        print("\n❌ 测试失败，请检查实现。")
