/**
 * ConversationDetailModal Component
 *
 * A modal for displaying conversation details with tabs for history and logs
 * Follows Apple/OpenAI-inspired aesthetic with responsive design
 * Redesigned for a more minimalist, elegant interface
 * Uses ChatArea component for consistent message rendering with Markdown support
 */
import { ref, computed, watch, onMounted, onUnmounted, inject } from 'vue';
import {
    ChatBubbleLeftRightIcon,
    TerminalIcon,
    ExclamationTriangleIcon
} from '../../../utils/Icons.js';
import { getResolvedTheme } from '../../../utils/ThemeManager.js';
import BaseDashboardCard from '../cards/BaseDashboardCard.js';
import ChatArea from '../../../chatbi/components/ChatArea.js';
import ImageGallery from '../common/ImageGallery.js';
import { updateRepairStatus } from '../../services/conversationService.js';

export default {
    name: 'ConversationDetailModal',
    components: {
        BaseDashboardCard,
        ChatArea,
        ImageGallery
    },
    props: {
        isOpen: {
            type: Boolean,
            required: true
        },
        conversation: {
            type: Object,
            default: null
        }
    },
    emits: ['close', 'conversation-updated'],
    setup(props, { emit }) {
        // Active tab state
        const activeTab = ref('history'); // 'history' or 'logs'



        // Track current theme
        const isDarkTheme = ref(getResolvedTheme() === 'dark');

        // Toast 通知状态
        const toastState = ref({
            isVisible: false,
            message: '',
            type: 'success',
            isLoading: false
        });

        // 显示 Toast 通知
        const showToast = (message, type = 'success', duration = 2000, isLoading = false) => {
            toastState.value = {
                isVisible: true,
                message,
                type,
                isLoading
            };

            if (!isLoading && duration > 0) {
                setTimeout(() => {
                    toastState.value.isVisible = false;
                }, duration);
            }
        };

        // 隐藏 Toast 通知
        const hideToast = () => {
            toastState.value.isVisible = false;
        };

        // Inject shared services
        const shareConversation = inject('shareConversation');

        // Repair status state
        const isUpdatingRepairStatus = ref(false);
        const repairNote = ref('');
        const showRepairNoteInput = ref(false);
        const pendingRepairStatus = ref(null); // 保存用户选择的待处理状态

        // Get repair status display info
        const getRepairStatusInfo = (repairStatus) => {
            switch (repairStatus) {
                case 0:
                    return { text: '未修复', class: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' };
                case 1:
                    return { text: '已修复', class: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' };
                case 2:
                    return { text: '暂不修复', class: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' };
                default:
                    return null;
            }
        };

        // 计算当前应该显示的修复状态值
        const currentRepairStatusValue = computed(() => {
            // 如果正在显示输入框，显示原来的状态，否则显示当前状态
            if (showRepairNoteInput.value && props.conversation) {
                return props.conversation.repair_status || 0;
            }
            return props.conversation ? (props.conversation.repair_status || 0) : 0;
        });

        // Handle repair status change (show note input for status 1 or 2)
        const handleRepairStatusChange = (newStatus) => {
            if (newStatus === 1 || newStatus === 2) {
                // 对于"已修复"或"暂不修复"状态，保存选择的状态并显示修复说明输入框
                pendingRepairStatus.value = newStatus;
                showRepairNoteInput.value = true;
                repairNote.value = '';
            } else {
                // 对于"未修复"状态，直接更新
                pendingRepairStatus.value = null;
                handleRepairStatusUpdate(newStatus, '');
            }
        };

        // Handle repair status update
        const handleRepairStatusUpdate = async (newStatus, note = '') => {
            if (!props.conversation || isUpdatingRepairStatus.value) return;

            try {
                isUpdatingRepairStatus.value = true;
                showToast('正在更新修复状态...', 'info', 0, true);

                await updateRepairStatus(
                    props.conversation.conversation_id,
                    newStatus,
                    props.conversation.username,
                    props.conversation.email,
                    note || null
                );

                // Update local state
                props.conversation.repair_status = newStatus;
                props.conversation.repairStatus = newStatus;
                if (note) {
                    props.conversation.repair_note = note;
                }

                hideToast();
                showToast('修复状态已更新', 'success');

                // Hide repair note input
                showRepairNoteInput.value = false;
                repairNote.value = '';

                // Notify parent component
                emit('conversation-updated', props.conversation);
            } catch (error) {
                console.error('更新修复状态失败:', error);
                hideToast();
                showToast('更新失败，请稍后重试', 'error');
            } finally {
                isUpdatingRepairStatus.value = false;
            }
        };

        // Handle repair note submission
        const submitRepairNote = () => {
            if (pendingRepairStatus.value !== null) {
                handleRepairStatusUpdate(pendingRepairStatus.value, repairNote.value);
                pendingRepairStatus.value = null;
            }
        };

        // Cancel repair note input
        const cancelRepairNote = () => {
            showRepairNoteInput.value = false;
            repairNote.value = '';
            pendingRepairStatus.value = null;
            // 不需要重置select的值，因为我们使用Vue的响应式绑定
        };

        // Parse user message content to extract text and images
        const parseUserMessage = (message) => {
            if (message.role !== 'user') {
                return { text: message.content, images: [] };
            }

            // 处理resource_url字段中的图片
            let images = [];
            if (message.resource_url && typeof message.resource_url === 'string') {
                images = message.resource_url.split(',').map(url => url.trim()).filter(url => url.length > 0);
            }

            // 检查是否是旧的JSON格式（向后兼容）
            try {
                const parsed = JSON.parse(message.content);
                if (parsed && typeof parsed === 'object' && 'text' in parsed) {
                    return {
                        text: parsed.text || '',
                        images: parsed.images || images
                    };
                }
            } catch (e) {
                // 不是JSON格式，当作纯文本处理
            }

            return {
                text: message.content,
                images: images
            };
        };

        // Computed property for formatted messages
        const formattedMessages = computed(() => {
            if (!props.conversation) {
                return [];
            }

            if (!props.conversation.messages) {
                return [];
            }

            const formatted = props.conversation.messages.map(msg => {
                const parsed = parseUserMessage(msg);
                return {
                    id: String(msg.id), // 确保id是字符串类型
                    role: msg.role,
                    content: parsed.text,
                    images: parsed.images,
                    timestamp: new Date(msg.timestamp).toLocaleString(),
                    time_spend: msg.time_spend, // 添加 time_spend 字段
                    time_to_first_token: msg.time_to_first_token, // 添加 time_to_first_token 字段
                    // 使用对话级别的状态，而不是消息级别的状态
                    isBadCase: Boolean(props.conversation.hasBadCase || props.conversation.is_bad_case),
                    isGoodCase: Boolean(props.conversation.hasGoodCase || props.conversation.is_good_case),
                    // 添加反馈信息 - 使用对话级别的反馈信息
                    goodCaseFeedback: props.conversation?.messages?.[0]?.goodCaseFeedback || null,
                    badCaseFeedback: props.conversation?.messages?.[0]?.badCaseFeedback || null,
                    username: msg.username,
                    email: msg.email
                };
            });

            return formatted;
        });

        // Computed property for logs
        const logs = computed(() => {
            if (!props.conversation || !props.conversation.messages) {
                return [];
            }

            // Extract logs from assistant messages
            const allLogs = props.conversation.messages
                .filter(msg => msg.role === 'assistant' && msg.logs)
                .map(msg => ({
                    id: String(msg.id), // 确保id是字符串类型
                    logs: msg.logs,
                    timestamp: new Date(msg.timestamp).toLocaleString()
                }));

            return allLogs;
        });

        // Handle close
        const handleClose = () => {
            emit('close');
            // Reset tab when modal closes
            setTimeout(() => {
                activeTab.value = 'history';
            }, 300);
        };



        // Switch tab
        const switchTab = (tab) => {
            activeTab.value = tab;
        };

        // Reset state when conversation changes
        watch(() => props.conversation, () => {
            // Reset any state if needed
        });

        // Watch for theme changes
        const themeObserver = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    isDarkTheme.value = document.documentElement.getAttribute('data-theme') === 'dark';
                }
            }
        });

        // Start observing theme changes
        onMounted(() => {
            themeObserver.observe(document.documentElement, { attributes: true });
        });

        // Clean up observer
        onUnmounted(() => {
            themeObserver.disconnect();
        });

        // Handle message marked as bad case
        const handleMessageMarkedAsBadCase = async () => {
            if (!props.conversation) return;

            // AiMessage组件已经调用了API，这里只需要更新本地状态
            const newStatus = !(props.conversation.hasBadCase || props.conversation.is_bad_case);

            // 更新本地状态 - 使用新字段名
            props.conversation.hasBadCase = newStatus;
            // 为了向后兼容，同时更新旧字段名
            props.conversation.is_bad_case = newStatus;
            props.conversation.isBadCase = newStatus;

            // 显示成功的 toast
            showToast(newStatus ? '已标记为 Bad Case' : '已取消标记', 'success');

            // 通知父组件对话已更新
            emit('conversation-updated', props.conversation);
        };

        // Handle message marked as good case
        const handleMessageMarkedAsGoodCase = async () => {
            if (!props.conversation) return;

            // AiMessage组件已经调用了API，这里只需要更新本地状态
            const newStatus = !(props.conversation.hasGoodCase || props.conversation.is_good_case);

            // 更新本地状态 - 使用新字段名
            props.conversation.hasGoodCase = newStatus;
            // 为了向后兼容，同时更新旧字段名
            props.conversation.is_good_case = newStatus;
            props.conversation.isGoodCase = newStatus;

            // 显示成功的 toast
            showToast(newStatus ? '已标记为 Good Case' : '已取消标记', 'success');

            // 通知父组件对话已更新
            emit('conversation-updated', props.conversation);
        };

        // Handle share conversation
        const handleShareConversation = () => {
            if (!props.conversation) return;

            // 调用分享函数
            shareConversation(props.conversation.conversation_id);
        };

        // 反馈数据处理方法
        const hasFeedbackData = (conversation) => {
            return getGoodCaseFeedback(conversation) || getBadCaseFeedback(conversation);
        };

        const getGoodCaseFeedback = (conversation) => {
            if (!conversation || !conversation.messages || conversation.messages.length === 0) return null;

            // 查找第一个有good case反馈的消息
            const messageWithFeedback = conversation.messages.find(message =>
                message.goodCaseFeedback && message.goodCaseFeedback.feedback_submitted_at
            );
            return messageWithFeedback ? messageWithFeedback.goodCaseFeedback : null;
        };

        const getBadCaseFeedback = (conversation) => {
            if (!conversation || !conversation.messages || conversation.messages.length === 0) return null;

            // 查找第一个有bad case反馈的消息
            const messageWithFeedback = conversation.messages.find(message =>
                message.badCaseFeedback && message.badCaseFeedback.feedback_submitted_at
            );
            return messageWithFeedback ? messageWithFeedback.badCaseFeedback : null;
        };



        return {
            activeTab,
            formattedMessages,
            logs,
            handleClose,
            switchTab,
            isDarkTheme,
            handleMessageMarkedAsBadCase,
            handleMessageMarkedAsGoodCase,
            handleShareConversation,
            // Repair status
            isUpdatingRepairStatus,
            repairNote,
            showRepairNoteInput,
            pendingRepairStatus,
            currentRepairStatusValue,
            getRepairStatusInfo,
            handleRepairStatusChange,
            handleRepairStatusUpdate,
            submitRepairNote,
            cancelRepairNote,
            // Toast 状态
            toastState,
            // 反馈相关方法
            hasFeedbackData,
            getGoodCaseFeedback,
            getBadCaseFeedback,
            // Icons
            ChatBubbleLeftRightIcon,
            TerminalIcon,
            ExclamationTriangleIcon
        };
    },
    template: `
        <dialog :open="isOpen" class="modal modal-middle">
            <div class="modal-box max-w-4xl p-0 overflow-hidden bg-base-100 rounded-xl shadow-lg border border-base-200">
                <BaseDashboardCard size="auto" card-class="border-0 shadow-none">
                    <template #header>
                        <div class="flex justify-between items-start w-full">
                            <div class="flex flex-col">
                                <div class="flex items-center gap-2 flex-wrap mb-1">
                                    <h3 class="font-semibold text-base text-base-content tracking-tight">
                                        {{ conversation ? (conversation.conversation_id || conversation.id) : '会话详情' }}
                                    </h3>
                                    <div class="flex items-center gap-1">
                                        <span v-if="conversation && (conversation.hasBadCase || conversation.is_bad_case)"
                                            class="badge badge-sm border-none"
                                            :class="{'bg-rose-100 text-rose-700': !isDarkTheme, 'dark-mode-badge': isDarkTheme}">
                                            Bad Case
                                        </span>
                                        <span v-if="conversation && (conversation.hasGoodCase || conversation.is_good_case)"
                                            class="badge badge-sm border-none"
                                            :class="{'bg-emerald-100 text-emerald-700': !isDarkTheme, 'bg-emerald-900/30 text-emerald-300': isDarkTheme}">
                                            Good Case
                                        </span>
                                        <span v-if="conversation && (conversation.hasBadCase || conversation.is_bad_case) && conversation.repair_status !== undefined && getRepairStatusInfo(conversation.repair_status)"
                                              class="badge badge-sm border-none"
                                              :class="getRepairStatusInfo(conversation.repair_status).class">
                                            {{ getRepairStatusInfo(conversation.repair_status).text }}
                                        </span>
                                    </div>
                                </div>
                                <!-- 用户信息作为副标题 -->
                                <div v-if="conversation" class="flex items-center gap-2 text-xs text-base-content/60">
                                    <span class="font-medium">{{ conversation.username }}</span>
                                    <span>{{ conversation.email }}</span>
                                </div>
                            </div>

                            <!-- 只保留关闭按钮 -->
                            <button
                                class="btn btn-sm btn-ghost"
                                :class="isDarkTheme ? 'hover:bg-base-300/50 text-base-content/60 hover:text-base-content' : 'hover:bg-base-200 text-base-content/50 hover:text-base-content/80'"
                                @click="handleClose"
                                title="关闭"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                    </template>

                    <!-- Modal Content -->
                    <div class="overflow-y-auto max-h-[60vh] scrollbar-auto">
                        <!-- 详细内容 -->
                        <div class="space-y-5">

                            <!-- Repair Status Editor (only for bad cases) -->
                            <div v-if="conversation && (conversation.hasBadCase || conversation.is_bad_case)" class="space-y-3">
                                <div class="flex items-center gap-3">
                                    <span class="text-xs text-base-content/70 font-medium">修复状态:</span>
                                    <select
                                        :value="currentRepairStatusValue"
                                        @change="handleRepairStatusChange(parseInt($event.target.value))"
                                        :disabled="isUpdatingRepairStatus || showRepairNoteInput"
                                        class="repair-status-select select select-xs select-bordered w-auto min-w-0 text-xs dark:border-base-content/20 dark:border"
                                    >
                                        <option :value="0">未修复</option>
                                        <option :value="1">已修复</option>
                                        <option :value="2">暂不修复</option>
                                    </select>
                                    <span v-if="isUpdatingRepairStatus" class="loading loading-spinner loading-sm"></span>
                                </div>

                                <!-- Repair Note Input (shown when status is 1 or 2) -->
                                <div v-if="showRepairNoteInput" class="space-y-3">
                                    <div class="flex flex-col gap-2">
                                        <label class="text-xs text-base-content/70 font-medium">
                                            修复说明 (状态: {{ pendingRepairStatus === 1 ? '已修复' : '暂不修复' }}):
                                        </label>
                                        <textarea
                                            v-model="repairNote"
                                            placeholder="请描述如何修复了这个问题..."
                                            class="textarea textarea-bordered w-full text-sm resize-none dark:border-base-content/20 dark:border"
                                            rows="3"
                                            :disabled="isUpdatingRepairStatus"
                                        ></textarea>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <button
                                            @click="submitRepairNote"
                                            :disabled="isUpdatingRepairStatus"
                                            class="btn btn-sm btn-primary"
                                        >
                                            确认
                                        </button>
                                        <button
                                            @click="cancelRepairNote"
                                            :disabled="isUpdatingRepairStatus"
                                            class="btn btn-sm btn-ghost"
                                        >
                                            取消
                                        </button>
                                    </div>
                                </div>

                                <!-- Display existing repair note -->
                                <div v-if="conversation.repair_note && !showRepairNoteInput" class="space-y-2">
                                    <div class="text-xs text-base-content/70 font-medium">修复说明:</div>
                                    <div class="text-xs bg-base-200 p-3 rounded border dark:bg-base-300 leading-relaxed">
                                        {{ conversation.repair_note }}
                                    </div>
                                </div>
                            </div>

                            <!-- 用户反馈部分 -->
                            <div v-if="hasFeedbackData(conversation)" class="space-y-3">
                                <div class="text-xs font-medium text-base-content/70">
                                    用户反馈
                                </div>

                                <!-- Good Case 反馈 -->
                                <div v-if="(conversation.hasGoodCase || conversation.is_good_case) && getGoodCaseFeedback(conversation)" class="space-y-2">
                                    <div class="flex items-center flex-wrap gap-2">
                                        <span class="text-xs px-2.5 py-1 rounded-full font-medium"
                                              :class="isDarkTheme ? 'bg-green-900/40 text-green-300' : 'bg-green-100 text-green-700'">
                                            好评
                                        </span>
                                        <!-- 反馈标签 -->
                                        <template v-if="getGoodCaseFeedback(conversation).feedback_tags && getGoodCaseFeedback(conversation).feedback_tags.length > 0">
                                            <span v-for="tag in getGoodCaseFeedback(conversation).feedback_tags" :key="tag"
                                                  class="text-xs px-2.5 py-1 rounded-full"
                                                  :class="isDarkTheme ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-50 text-blue-600'">
                                                {{ tag }}
                                            </span>
                                        </template>
                                    </div>

                                    <!-- 自定义反馈文本 -->
                                    <div v-if="getGoodCaseFeedback(conversation).custom_feedback"
                                         class="text-xs text-base-content/70 pl-3 border-l-2 border-green-300 py-1">
                                        "{{ getGoodCaseFeedback(conversation).custom_feedback }}"
                                    </div>
                                </div>

                                <!-- Bad Case 反馈 -->
                                <div v-if="(conversation.hasBadCase || conversation.is_bad_case) && getBadCaseFeedback(conversation)" class="space-y-2">
                                    <div class="flex items-center flex-wrap gap-2">
                                        <span class="text-xs px-2.5 py-1 rounded-full font-medium"
                                              :class="isDarkTheme ? 'bg-red-900/40 text-red-300' : 'bg-red-100 text-red-700'">
                                            差评
                                        </span>
                                        <!-- 反馈标签 -->
                                        <template v-if="getBadCaseFeedback(conversation).feedback_tags && getBadCaseFeedback(conversation).feedback_tags.length > 0">
                                            <span v-for="tag in getBadCaseFeedback(conversation).feedback_tags" :key="tag"
                                                  class="text-xs px-2.5 py-1 rounded-full"
                                                  :class="isDarkTheme ? 'bg-orange-900/30 text-orange-300' : 'bg-orange-50 text-orange-600'">
                                                {{ tag }}
                                            </span>
                                        </template>
                                    </div>

                                    <!-- 自定义反馈文本 -->
                                    <div v-if="getBadCaseFeedback(conversation).custom_feedback"
                                         class="text-xs text-base-content/70 pl-3 border-l-2 border-red-300 py-1">
                                        "{{ getBadCaseFeedback(conversation).custom_feedback }}"
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tabs -->
                        <div>
                            <div class="flex">
                                <button
                                    class="px-6 py-3 text-sm font-medium transition-colors relative"
                                    :class="[
                                        activeTab === 'history'
                                            ? (isDarkTheme ? 'dark-mode-tab-active' : 'text-indigo-600 bg-base-50')
                                            : 'text-base-content/70 hover:text-base-content hover:bg-base-50'
                                    ]"
                                    @click="switchTab('history')"
                                >
                                    会话历史
                                    <div v-if="activeTab === 'history'" class="absolute bottom-0 left-0 w-full h-0.5"
                                         :class="isDarkTheme ? 'dark-mode-tab-indicator' : 'bg-indigo-500'"></div>
                                </button>
                                <button
                                    class="px-6 py-3 text-sm font-medium transition-colors relative"
                                    :class="[
                                        activeTab === 'logs'
                                            ? (isDarkTheme ? 'dark-mode-tab-active' : 'text-indigo-600 bg-base-50')
                                            : 'text-base-content/70 hover:text-base-content hover:bg-base-50'
                                    ]"
                                    @click="switchTab('logs')"
                                >
                                    系统日志
                                    <div v-if="activeTab === 'logs'" class="absolute bottom-0 left-0 w-full h-0.5"
                                         :class="isDarkTheme ? 'dark-mode-tab-indicator' : 'bg-indigo-500'"></div>
                                </button>
                            </div>
                        </div>

                        <!-- Tabs Divider -->
                        <div class="border-b border-base-200 dark:border-base-content/10"></div>

                        <!-- Loading State -->
                        <div v-if="!conversation" class="flex items-center justify-center py-12 px-6">
                            <span class="loading loading-spinner loading-md"></span>
                        </div>

                        <!-- History Tab -->
                        <div v-else-if="activeTab === 'history'" class="scrollbar-auto pt-4">
                            <div v-if="formattedMessages.length === 0" class="text-center py-8 px-6 text-base-content/60">
                                暂无会话历史
                            </div>
                            <div v-else>
                                <ChatArea
                                    :messages="formattedMessages"
                                    :conversation-id="conversation ? (conversation.conversation_id || conversation.id) : ''"
                                    :showToastNotifications="false"
                                    :allowUnmarkBadCase="true"
                                    :allowUnmarkGoodCase="true"
                                    @message-marked-as-bad-case="handleMessageMarkedAsBadCase"
                                    @message-marked-as-good-case="handleMessageMarkedAsGoodCase"
                                    @share-conversation="handleShareConversation"
                                />
                            </div>
                        </div>

                        <!-- Logs Tab -->
                        <div v-else-if="activeTab === 'logs'" class="space-y-4 scrollbar-auto">
                            <div v-if="logs.length === 0" class="text-center py-8 text-base-content/60">
                                暂无日志信息
                            </div>

                            <div v-for="log in logs" :key="log.id" class="log-entry">
                                <div class="text-xs opacity-60 mb-1">{{ log.timestamp }}</div>
                                <pre class="p-3 rounded-lg text-xs font-mono whitespace-pre-wrap overflow-x-auto border border-base-300/50 scrollbar-auto"
                                     :class="isDarkTheme ? 'bg-base-300/70' : 'bg-base-200/70'">{{ log.logs }}</pre>
                            </div>
                        </div>
                    </div>
                </BaseDashboardCard>
            </div>

            <!-- Modal Backdrop -->
            <form method="dialog" class="modal-backdrop bg-black/40" @click="handleClose">
                <button class="sr-only">关闭</button>
            </form>

            <!-- Toast 通知 -->
            <div v-if="toastState.isVisible" class="toast toast-center z-50" style="bottom: 20px;">
                <div class="alert glass-effect px-4 py-3" :class="{
                    'alert-success': toastState.type === 'success',
                    'alert-error': toastState.type === 'error',
                    'alert-info': toastState.type === 'info',
                    'alert-warning': toastState.type === 'warning'
                }">
                    <!-- 加载中状态 -->
                    <span v-if="toastState.isLoading" class="loading loading-spinner loading-xs mr-2"></span>
                    <!-- 成功状态 -->
                    <svg v-else-if="toastState.type === 'success'" xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <!-- 错误状态 -->
                    <svg v-else-if="toastState.type === 'error'" xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <!-- 信息状态 -->
                    <svg v-else-if="toastState.type === 'info'" xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <!-- 警告状态 -->
                    <svg v-else-if="toastState.type === 'warning'" xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <span class="text-sm font-medium">{{ toastState.message }}</span>
                </div>
            </div>
        </dialog>
    `
};
