/**
 * OverviewLayout Component
 *
 * Dashboard overview section with stats cards and charts
 * Fetches real data from dashboard APIs
 */
import { ref, reactive, onMounted, watch } from 'vue';
import StatsColumn from '../components/StatsColumn.js';
import ChartsGrid from '../components/ChartsGrid.js';
import GroupSection from '../components/GroupSection.js';
import DateRangePicker from '../../common/components/DateRangePicker.js';
import DepartmentFilter from '../components/DepartmentFilter.js';
import {
    UserIcon,
    ChatBubbleLeftRightIcon,
    MagnifyingGlassIcon,
    ExclamationTriangleIcon
} from '../../utils/Icons.js';
import {
    fetchDashboardStats,
    fetchTopUsers,
    fetchTopAgents,
    fetchDepartmentStats
} from '../services/dashboardService.js';

export default {
    name: 'OverviewLayout',
    components: {
        StatsColumn,
        ChartsGrid,
        GroupSection,
        DateRangePicker,
        DepartmentFilter
    },
    setup() {
        // Loading states
        const isLoading = ref(true);

        // Date range state (YYYY-MM-DD)
        function formatDate(date) {
            return date.toISOString().slice(0, 10);
        }
        const today = new Date();
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(today.getDate() - 6); // 包含今天共7天
        const startDate = ref(formatDate(sevenDaysAgo));
        const endDate = ref(formatDate(today));

        // Admin filter state
        const filterAdmin = ref(false);

        // Department filter state
        const selectedDepartment = ref('');

        // Stats data
        const statsData = reactive([
            {
                label: '用户数',
                value: 0,
                icon: UserIcon,
                iconBgColor: 'bg-blue-100 dark-mode-blue-bg',
                iconColor: 'text-blue-600 dark-mode-blue-text'
            },
            {
                label: '会话数',
                value: 0,
                icon: ChatBubbleLeftRightIcon,
                iconBgColor: 'bg-purple-100 dark-mode-purple-bg',
                iconColor: 'text-purple-600 dark-mode-purple-text'
            },
            {
                label: '查询数',
                value: 0,
                icon: MagnifyingGlassIcon,
                iconBgColor: 'bg-emerald-100 dark-mode-emerald-bg',
                iconColor: 'text-emerald-600 dark-mode-emerald-text'
            },
            {
                label: 'Bad Case',
                value: 0,
                icon: ExclamationTriangleIcon,
                iconBgColor: 'bg-rose-100 dark-mode-rose-bg',
                iconColor: 'text-rose-600 dark-mode-rose-text'
            }
        ]);

        // Bar chart data for top users
        const topUsersData = reactive({
            labels: [],
            datasets: [
                {
                    label: '查询数',
                    data: []
                }
            ]
        });

        // Bar chart data for top agents
        const topAgentsData = reactive({
            labels: [],
            datasets: [
                {
                    label: '会话数',
                    data: []
                }
            ]
        });

        // Bar chart data for department stats
        const departmentStatsData = reactive({
            labels: [],
            datasets: [
                {
                    label: '查询数',
                    data: []
                }
            ]
        });

        // Charts collection for ChartsGrid
        const chartsData = reactive([
            {
                title: '活跃用户排名',
                subtitle: '按查询数量统计',
                type: 'bar',
                data: topUsersData,
                colors: {
                    light: {
                        backgroundColor: 'rgba(79, 70, 229, 0.3)',
                        borderColor: 'rgb(79, 70, 229)'
                    },
                    dark: {
                        backgroundColor: 'rgba(129, 140, 248, 0.3)',
                        borderColor: 'rgb(129, 140, 248)'
                    }
                }
            },
            {
                title: '热门助手排名',
                subtitle: '按会话数量统计',
                type: 'bar',
                data: topAgentsData,
                colors: {
                    light: {
                        backgroundColor: 'rgba(16, 185, 129, 0.3)',
                        borderColor: 'rgb(16, 185, 129)'
                    },
                    dark: {
                        backgroundColor: 'rgba(52, 211, 153, 0.3)',
                        borderColor: 'rgb(52, 211, 153)'
                    }
                }
            },
            {
                title: '部门提问排行',
                subtitle: '按用户提问数量统计',
                type: 'bar',
                data: departmentStatsData,
                colors: {
                    light: {
                        backgroundColor: 'rgba(236, 72, 153, 0.3)',
                        borderColor: 'rgb(236, 72, 153)'
                    },
                    dark: {
                        backgroundColor: 'rgba(244, 114, 182, 0.3)',
                        borderColor: 'rgb(244, 114, 182)'
                    }
                },
                chartOptions: {
                    indexAxis: 'y',  // 水平条形图
                    scales: {
                        x: {  // x轴是数值轴
                            type: 'logarithmic',
                            min: 1,  // 最小值设为1
                            max: 5000,  // 可选：设置最大值
                            ticks: {
                                callback: function(value) {
                                    // 显示主要的对数刻度点
                                    if ([1, 10, 100, 1000, 10000].includes(value)) {
                                        return value.toString();
                                    }
                                    return '';
                                }
                            },
                            grid: {
                                display: true
                            }
                        },
                        y: {  // y轴是分类轴
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            }
        ]);

        // Load dashboard statistics
        const loadStats = async () => {
            try {
                const data = await fetchDashboardStats(startDate.value, endDate.value, filterAdmin.value, selectedDepartment.value);
                // Update stats data
                statsData[0].value = data.total_users;
                statsData[1].value = data.total_conversations;
                statsData[2].value = data.total_queries;
                statsData[3].value = data.bad_case_count;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        };

        // Load top users data
        const loadTopUsers = async () => {
            try {
                const topUsers = await fetchTopUsers(startDate.value, endDate.value, filterAdmin.value, 5, selectedDepartment.value);
                // Update top users chart data
                topUsersData.labels = topUsers.map(user => user.username || '未知用户');
                topUsersData.datasets[0].data = topUsers.map(user => user.query_count);
            } catch (error) {
                console.error('Error loading top users:', error);
            }
        };

        // 加载Top Agents数据
        const loadTopAgents = async () => {
            try {
                const agentStats = await fetchTopAgents(startDate.value, endDate.value, filterAdmin.value, 5, selectedDepartment.value);

                if (agentStats && Array.isArray(agentStats)) {
                    // 直接使用后端返回的agent名称，不进行任何转换
                    topAgentsData.labels = agentStats.map(stat => stat.agent_name);
                    topAgentsData.datasets[0].data = agentStats.map(stat => stat.conversation_count);

                    console.log(`加载了 ${agentStats.length} 个Top Agents数据`);
                } else {
                    console.warn('Top Agents API返回数据格式异常:', agentStats);
                }
            } catch (error) {
                console.error('Error loading top agents:', error);
            }
        };

        // 加载部门统计数据
        const loadDepartmentStats = async () => {
            try {
                const departmentStats = await fetchDepartmentStats(startDate.value, endDate.value, filterAdmin.value, 5);

                if (departmentStats && Array.isArray(departmentStats)) {
                    // 更新部门统计图表数据
                    departmentStatsData.labels = departmentStats.map(stat => stat.department_name || '未知部门');
                    departmentStatsData.datasets[0].data = departmentStats.map(stat => stat.user_query_count);

                    console.log(`加载了 ${departmentStats.length} 个部门统计数据`);
                } else {
                    console.warn('部门统计API返回数据格式异常:', departmentStats);
                }
            } catch (error) {
                console.error('Error loading department stats:', error);
            }
        };

        // 加载所有dashboard数据
        const loadDashboardData = async () => {
            isLoading.value = true;

            try {
                // 并行加载所有数据
                await Promise.all([
                    loadStats(),
                    loadTopUsers(),
                    loadTopAgents(),
                    loadDepartmentStats()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            } finally {
                isLoading.value = false;
            }
        };

        // Watch for date range changes
        watch([startDate, endDate], (newValues) => {
            const [newStartDate, newEndDate] = newValues;

            // 只有当开始日期和结束日期都已设置时才触发数据加载
            // 清除日期的情况由 DateRangePicker 的 @clear 事件处理
            if (newStartDate && newEndDate) {
                loadDashboardData();
            }
            if (!newStartDate && !newEndDate) {
                loadDashboardData();
            }
        });

        // Handle admin filter change
        const handleFilterAdminChange = (value) => {
            filterAdmin.value = value;
            loadDashboardData();
        };

        // Handle department filter change
        const handleDepartmentChange = (department) => {
            selectedDepartment.value = department;
            loadDashboardData();
        };

        // Load data when component is mounted
        onMounted(() => {
            loadDashboardData();
        });

        return {
            isLoading,
            startDate,
            endDate,
            filterAdmin,
            selectedDepartment,
            statsData,
            chartsData,
            handleFilterAdminChange,
            handleDepartmentChange
        };
    },
    template: `
        <GroupSection group-id="overview">
            <template #title>
                <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-3">
                    <h2 class="text-l font-bold">统计信息</h2>

                    <div class="flex flex-col sm:flex-row items-end sm:items-center gap-3">
                        <!-- Date Range Picker Component -->
                        <DateRangePicker
                            :start-date="startDate"
                            :end-date="endDate"
                            @update:startDate="val => startDate = val"
                            @update:endDate="val => endDate = val"
                            @clear="() => {
                                // 只有当用户点击叉号清除日期时才会触发此事件
                                // 此时两个日期值已经被设置为空字符串
                                loadDashboardData();
                            }"
                            label="日期范围"
                        />


                        <!-- Admin Filter Toggle -->
                        <div class="flex items-center gap-2 bg-base-200/50 px-3 py-1.5 rounded-lg border border-base-300/50">
                            <span class="text-sm font-medium text-base-content/80">过滤admin</span>
                            <label class="cursor-pointer inline-flex items-center">
                                <input
                                    type="checkbox"
                                    class="sr-only peer"
                                    :checked="filterAdmin"
                                    @change="(event) => handleFilterAdminChange(event.target.checked)"
                                />
                                <div class="relative w-10 h-5 bg-base-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                            </label>
                        </div>

                        <!-- Department Filter -->
                        <DepartmentFilter
                            :initial-department="selectedDepartment"
                            @department-change="handleDepartmentChange"
                        />
                    </div>
                </div>
            </template>

            <div class="space-y-6">
                <!-- Top Row - Stats Cards (Full Width) -->
                <div class="w-full">
                    <StatsColumn :stats="statsData" :is-loading="isLoading" />
                </div>

                <!-- Bottom Row - Charts Grid (Full Width) -->
                <div class="w-full">
                    <ChartsGrid :charts="chartsData" :is-loading="isLoading" />
                </div>
            </div>
        </GroupSection>
    `
};