import { ref, watch, onMounted } from 'vue';
import { CopyIcon, CheckIcon, ThumbsDownIcon, ThumbsUpIcon, ExternalLinkIcon } from '../../utils/Icons.js';
import { renderMarkdown } from '../../utils/MarkdownRenderer.js';
import { markConversationAsBadCase, markConversationAsGoodCase, submitConversationFeedback, markChatHistoryAsBadCase, markChatHistoryAsGoodCase } from '../services/historyService.js';
import FeedbackModal from './modals/FeedbackModal.js';

export default {
    name: 'AiMessage',
    components: {
        FeedbackModal
    },
    props: {
        content: {
            type: String,
            required: true
        },
        renderedContent: {
            type: String,
            default: ''
        },
        updatedAt: {
            type: String,
            default: ''
        },
        timeSpend: {
            type: Number,
            default: null
        },
        timeToFirstToken: {
            type: Number,
            default: null
        },
        isStreaming: {
            type: Boolean,
            default: false
        },
        isError: {
            type: Boolean,
            default: false
        },
        isInterrupted: {
            type: Boolean,
            default: false
        },
        conversationId: {
            type: String,
            default: ''
        },
        isBadCase: {
            type: Boolean,
            default: false
        },
        isGoodCase: {
            type: Boolean,
            default: false
        },
        showToastNotifications: {
            type: Boolean,
            default: true
        },
        allowUnmarkBadCase: {
            type: Boolean,
            default: false
        },
        allowUnmarkGoodCase: {
            type: Boolean,
            default: true
        },
        goodCaseFeedback: {
            type: Object,
            default: null
        },
        badCaseFeedback: {
            type: Object,
            default: null
        },
        // 新增：支持基于chat_history_id的标记
        chatHistoryId: {
            type: Number,
            default: null
        },
        // 新增：控制是否使用消息级别标记（而不是对话级别）
        useMessageLevelMarking: {
            type: Boolean,
            default: false
        }
    },
    emits: ['marked-as-bad-case', 'marked-as-good-case', 'share-conversation'],
    setup(props, { emit }) {
        const copied = ref(false);
        const renderedHtml = ref('');
        const isMarkedAsBadCase = ref(props.isBadCase);
        const isMarkedAsGoodCase = ref(props.isGoodCase);
        const lastRenderedContent = ref(''); // 用于跟踪上次渲染的内容
        const showToast = ref(false);
        const isMarkingBadCase = ref(false);
        const isMarkingGoodCase = ref(false);

        // 反馈相关状态
        const showFeedbackModal = ref(false);
        const feedbackType = ref(null); // 'positive' | 'negative'
        const isSubmittingFeedback = ref(false);
        const buttonExtended = ref({ good: false, bad: false });
        const feedbackSubmitted = ref({ good: false, bad: false });

        // 初始化反馈状态
        const initializeFeedbackState = () => {
            // 检查是否有好评反馈
            if (props.goodCaseFeedback && props.goodCaseFeedback.feedback_submitted_at) {
                // 已标记为好评并且已提交反馈
                buttonExtended.value.good = true;
                feedbackSubmitted.value.good = true;
            } else if (props.goodCaseFeedback || props.isGoodCase) {
                // 已标记为好评但没有提交反馈，显示填写反馈
                buttonExtended.value.good = true;
                feedbackSubmitted.value.good = false;
            }

            // 检查是否有差评反馈
            if (props.badCaseFeedback && props.badCaseFeedback.feedback_submitted_at) {
                // 已标记为差评并且已提交反馈
                buttonExtended.value.bad = true;
                feedbackSubmitted.value.bad = true;
            } else if (props.badCaseFeedback || props.isBadCase) {
                // 已标记为差评但没有提交反馈，显示填写反馈
                buttonExtended.value.bad = true;
                feedbackSubmitted.value.bad = false;
            }
        };

        // 复制消息内容
        const copyContent = (text) => {
            navigator.clipboard.writeText(text)
                .then(() => {
                    copied.value = true;
                    setTimeout(() => {
                        copied.value = false;
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                });
        };

        // 分享对话
        const shareConversation = () => {
            if (!props.conversationId || props.isStreaming) return;
            emit('share-conversation', props.conversationId);
        };

        // 通用标记函数 - 遵循DRY原则
        const markAsCase = async (caseType) => {
            // 检查是否有必要的ID和是否正在流式传输
            if (props.isStreaming) return;
            if (props.useMessageLevelMarking && !props.chatHistoryId) return;
            if (!props.useMessageLevelMarking && !props.conversationId) return;

            const isBadCase = caseType === 'bad';
            const currentMarkedState = isBadCase ? isMarkedAsBadCase : isMarkedAsGoodCase;
            const allowUnmark = isBadCase ? props.allowUnmarkBadCase : props.allowUnmarkGoodCase;
            const markingState = isBadCase ? isMarkingBadCase : isMarkingGoodCase;

            // 根据允许取消标记属性决定新状态
            const newStatus = allowUnmark ? !currentMarkedState.value : true;

            // 如果当前已经标记且不允许取消标记，则直接返回
            if (currentMarkedState.value && !allowUnmark) {
                return;
            }

            // 只有在启用 toast 通知时才显示
            if (props.showToastNotifications) {
                markingState.value = true;
                showToast.value = true;
            }

            try {
                // 根据标记级别和类型调用不同的API
                if (props.useMessageLevelMarking) {
                    if (isBadCase) {
                        await markChatHistoryAsBadCase(props.chatHistoryId, newStatus);
                    } else {
                        await markChatHistoryAsGoodCase(props.chatHistoryId, newStatus);
                    }
                } else {
                    if (isBadCase) {
                        await markConversationAsBadCase(props.conversationId, newStatus);
                    } else {
                        await markConversationAsGoodCase(props.conversationId, newStatus);
                    }
                }

                // 更新本地状态
                currentMarkedState.value = newStatus;

                // 发送事件通知父组件，传递相应的ID
                const eventData = props.useMessageLevelMarking ?
                    { type: 'message', id: props.chatHistoryId } :
                    { type: 'conversation', id: props.conversationId };
                const eventName = isBadCase ? 'marked-as-bad-case' : 'marked-as-good-case';
                emit(eventName, eventData);

                // 如果是标记，触发按钮扩展
                if (newStatus) {
                    buttonExtended.value[caseType] = true;
                }

                // 更新为标记成功状态
                markingState.value = false;

                // 只有在启用 toast 通知时才处理 toast
                if (props.showToastNotifications) {
                    setTimeout(() => {
                        showToast.value = false;
                    }, 2000);
                }
            } catch (error) {
                const errorMsg = isBadCase ? '标记不良案例失败' : '标记Good Case失败';
                console.error(`${errorMsg}:`, error);
                markingState.value = false;

                if (props.showToastNotifications) {
                    showToast.value = false;
                }
            }
        };

        // 标记或取消标记为不良案例
        const markAsBadCase = () => markAsCase('bad');

        // 标记或取消标记为Good Case
        const markAsGoodCase = () => markAsCase('good');

        // 反馈相关方法

        // 打开反馈模态框
        const openFeedbackModal = (type) => {
            feedbackType.value = type;
            showFeedbackModal.value = true;
        };

        // 关闭反馈模态框
        const closeFeedbackModal = () => {
            showFeedbackModal.value = false;
            feedbackType.value = null;
        };

        // 提交反馈
        const handleFeedbackSubmit = async (feedbackData) => {
            isSubmittingFeedback.value = true;

            try {
                await submitConversationFeedback(props.conversationId, feedbackType.value, feedbackData);

                // 更新反馈提交状态
                if (feedbackType.value === 'positive') {
                    feedbackSubmitted.value.good = true;
                } else {
                    feedbackSubmitted.value.bad = true;
                }

                closeFeedbackModal();

                // 可以在这里显示成功提示
                console.log('反馈提交成功');

            } catch (error) {
                console.error('提交反馈失败:', error);
                // 可以在这里显示错误提示
            } finally {
                isSubmittingFeedback.value = false;
            }
        };

        // 检查是否已有反馈
        const hasFeedbackForRating = (type) => {
            return type === 'positive' ? feedbackSubmitted.value.good : feedbackSubmitted.value.bad;
        };

        // 处理扩展按钮点击
        const handleExtendedButtonClick = (type) => {
            if (!hasFeedbackForRating(type)) {
                openFeedbackModal(type);
            }
        };

        // 组件挂载时初始化反馈状态
        onMounted(() => {
            initializeFeedbackState();
        });

        // 监听content和renderedContent变化，更新渲染的HTML - 使用增量渲染
        watch([() => props.content, () => props.renderedContent], ([newContent, newRenderedContent], [oldContent]) => {
            // 确保内容是字符串
            const contentStr = typeof newContent === 'string' ? newContent : (newContent ? JSON.stringify(newContent) : '');
            const oldContentStr = typeof oldContent === 'string' ? oldContent : (oldContent ? JSON.stringify(oldContent) : '');

            // 如果提供了预渲染的HTML，直接使用
            if (newRenderedContent) {
                renderedHtml.value = newRenderedContent;
                lastRenderedContent.value = contentStr; // 更新最后渲染的内容
                return;
            }

            // 如果内容没有变化，不做任何事
            if (contentStr === oldContentStr) return;

            // 如果内容是之前内容的扩展（流式更新的常见情况）
            if (contentStr.startsWith(lastRenderedContent.value) && lastRenderedContent.value.length > 0) {
                // 只渲染新增的部分
                const newPart = contentStr.slice(lastRenderedContent.value.length);
                if (newPart) {
                    try {
                        // 对于简单的文本追加，可以直接渲染并追加
                        // 但为了确保HTML结构正确，对于复杂内容还是重新渲染整个内容
                        if (newPart.length < 100 && !newPart.includes('```') && !newPart.includes('|')) {
                            // 简单文本追加 - 直接渲染新部分
                            const newPartHtml = renderMarkdown(newPart);
                            // 移除新部分HTML中的包装元素，只保留内部内容
                            const contentOnly = newPartHtml.replace(/<\/?p>/g, '');

                            // 如果当前HTML为空，直接设置
                            if (!renderedHtml.value) {
                                renderedHtml.value = newPartHtml;
                            } else {
                                // 查找最后一个段落的结束标签
                                const lastParagraphIndex = renderedHtml.value.lastIndexOf('</p>');
                                if (lastParagraphIndex !== -1) {
                                    // 在最后一个段落内追加内容
                                    renderedHtml.value =
                                        renderedHtml.value.slice(0, lastParagraphIndex) +
                                        contentOnly +
                                        renderedHtml.value.slice(lastParagraphIndex);
                                } else {
                                    // 如果没有找到段落标签，直接追加
                                    renderedHtml.value += contentOnly;
                                }
                            }
                        } else {
                            // 复杂内容 - 重新渲染整个内容
                            renderedHtml.value = renderMarkdown(contentStr);
                        }
                    } catch (error) {
                        // 如果增量渲染出错，回退到完整渲染
                        console.error('增量渲染失败，回退到完整渲染:', error);
                        renderedHtml.value = renderMarkdown(contentStr);
                    }
                }
            } else {
                // 如果内容完全改变，重新渲染整个内容
                renderedHtml.value = renderMarkdown(contentStr);
            }

            // 更新最后渲染的内容
            lastRenderedContent.value = contentStr;
        }, { immediate: true });

        // Watch for isBadCase prop changes
        watch(() => props.isBadCase, (newValue) => {
            isMarkedAsBadCase.value = newValue;
        });

        // Watch for isGoodCase prop changes
        watch(() => props.isGoodCase, (newValue) => {
            isMarkedAsGoodCase.value = newValue;
        });

        return {
            copyContent,
            markAsBadCase,
            markAsGoodCase,
            shareConversation,
            renderedHtml,
            copied,
            isMarkedAsBadCase,
            isMarkedAsGoodCase,
            showToast,
            isMarkingBadCase,
            isMarkingGoodCase,
            // 反馈相关状态和方法
            showFeedbackModal,
            feedbackType,
            isSubmittingFeedback,
            buttonExtended,
            feedbackSubmitted,
            openFeedbackModal,
            closeFeedbackModal,
            handleFeedbackSubmit,
            hasFeedbackForRating,
            handleExtendedButtonClick,
            // 图标
            CopyIcon,
            CheckIcon,
            ThumbsDownIcon,
            ThumbsUpIcon,
            ExternalLinkIcon
        };
    },
    template: `
        <div class="flex flex-col mb-6 group min-w-0 message ai-message"
             :class="{
                'ai-message-streaming': isStreaming,
                'ai-message-error': isError,
                'ai-message-interrupted': isInterrupted
             }">

            <div class="w-full px-4 py-3 pb-1 relative min-w-0 ai-message-content">
                <div class="markdown-content min-w-0" v-html="renderedHtml"></div>
                <div v-if="isStreaming" class="streaming-indicator">
                    <span class="loading loading-dots loading-sm"></span>
                </div>
            </div>

            <div class="flex items-center gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-all duration-300 pl-4 message-footer ai-message-footer">
                <button
                    v-if="!isStreaming"
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="copyContent(content)"
                    title="复制"
                >
                    <span v-if="!copied" class="message-action-icon" v-html="CopyIcon"></span>
                    <span v-else class="message-action-icon" v-html="CheckIcon"></span>
                </button>
                <button
                    v-if="!isStreaming && conversationId"
                    class="btn btn-xs btn-ghost message-action-button"
                    :class="{
                        'good-case-marked': isMarkedAsGoodCase,
                        'btn-square': !buttonExtended.good,
                        'extended-button': buttonExtended.good
                    }"
                    @click="buttonExtended.good ? handleExtendedButtonClick('positive') : markAsGoodCase()"
                    :title="isMarkedAsGoodCase && allowUnmarkGoodCase ? '取消标记' : '标记为Good Case'"
                >
                    <span class="message-action-icon" :class="{ 'good-case-icon': isMarkedAsGoodCase }" v-html="ThumbsUpIcon"></span>
                    <span v-if="buttonExtended.good" class="feedback-prompt-text"
                          :class="{
                              'clickable': !hasFeedbackForRating('positive'),
                              'submitted': hasFeedbackForRating('positive')
                          }">
                        {{ hasFeedbackForRating('positive') ? '感谢反馈' : '填写反馈' }}
                    </span>
                </button>
                <button
                    v-if="!isStreaming && conversationId"
                    class="btn btn-xs btn-ghost message-action-button"
                    :class="{
                        'bad-case-marked': isMarkedAsBadCase,
                        'btn-square': !buttonExtended.bad,
                        'extended-button': buttonExtended.bad
                    }"
                    @click="buttonExtended.bad ? handleExtendedButtonClick('negative') : markAsBadCase()"
                    :title="isMarkedAsBadCase && allowUnmarkBadCase ? '取消标记' : '标记为bad case'"
                >
                    <span class="message-action-icon" :class="{ 'bad-case-icon': isMarkedAsBadCase }" v-html="ThumbsDownIcon"></span>
                    <span v-if="buttonExtended.bad" class="feedback-prompt-text"
                          :class="{
                              'clickable': !hasFeedbackForRating('negative'),
                              'submitted': hasFeedbackForRating('negative')
                          }">
                        {{ hasFeedbackForRating('negative') ? '感谢反馈' : '填写反馈' }}
                    </span>
                </button>
                <button
                    v-if="!isStreaming && conversationId"
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="shareConversation"
                    title="分享对话"
                >
                    <span class="message-action-icon" v-html="ExternalLinkIcon"></span>
                </button>
                <span class="text-xs opacity-70 message-timestamp">
                    {{ isStreaming ? '正在生成...' : (isError ? '生成失败' : (isInterrupted ? '已中断' : updatedAt)) }}
                    <span v-if="!isStreaming && !isError && !isInterrupted && timeSpend !== null" class="text-red-500 font-medium ml-2">
                        time spend: {{ timeSpend }}s
                    </span>
                    <span v-if="!isStreaming && !isError && !isInterrupted && timeToFirstToken !== null && timeToFirstToken > 0" class="text-blue-500 font-medium ml-2">
                        first token: {{ timeToFirstToken }}s
                    </span>
                </span>
            </div>

            <!-- Toast 通知 - 简约而精致的设计，符合Apple/OpenAI风格 -->
            <div v-if="showToastNotifications && showToast" class="toast toast-center toast-bottom z-50">
                <div class="alert glass-effect px-4 py-3">
                    <!-- 标记中状态 - 显示更小更精致的spinner -->
                    <span v-if="isMarkingBadCase || isMarkingGoodCase" class="loading loading-spinner loading-xs text-success mr-2"></span>
                    <!-- 标记成功状态 - 显示勾选图标 -->
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="stroke-success flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span class="text-sm font-medium">
                        {{ isMarkingBadCase
                            ? (allowUnmarkBadCase && isMarkedAsBadCase ? '正在取消标记...' : '正在标记为不良案例...')
                            : isMarkingGoodCase
                            ? (allowUnmarkGoodCase && isMarkedAsGoodCase ? '正在取消标记...' : '正在标记为Good Case...')
                            : isMarkedAsBadCase
                            ? '已标记为不良案例'
                            : isMarkedAsGoodCase
                            ? '已标记为Good Case'
                            : '已取消标记'
                        }}
                    </span>
                </div>
            </div>

            <!-- 反馈收集模态框 -->
            <FeedbackModal
                :is-open="showFeedbackModal"
                :feedback-type="feedbackType"
                :is-submitting="isSubmittingFeedback"
                @close="closeFeedbackModal"
                @submit="handleFeedbackSubmit"
            />
        </div>
    `
};
