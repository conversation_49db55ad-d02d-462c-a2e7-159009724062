"""
Good case API endpoints.

This module provides API endpoints for managing good cases.
"""

from flask import request, jsonify, session, Blueprint

from src.services.auth.user_login_with_feishu import login_required
from src.services.dashboard.dashboard_service import admin_required
from src.services.chatbot.good_case_service import mark_good_case, mark_good_case_by_chat_history_id
from src.services.chatbot.history_service import check_conversation_ownership, check_chat_history_ownership
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email

# 创建一个 Blueprint，用于管理 good case
good_case_bp = Blueprint('good_case', __name__, url_prefix='')


@good_case_bp.route('/api/mark_conversation_as_good_case', methods=['POST'])
@login_required
def mark_conversation_as_good_case():
    """
    POST /api/mark_conversation_as_good_case 端点，将会话标记为Good Case。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "未在会话中找到用户信息"}), 401

    data = request.get_json()
    if not data or 'conversation_id' not in data:
        return jsonify({'error': '缺少会话ID'}), 400

    conversation_id = data['conversation_id']
    # 获取 is_good_case 参数，默认为 True（向后兼容）
    is_good_case = data.get('is_good_case', True)

    # 获取反馈数据（可选）
    feedback_tags = data.get('feedback_tags', [])
    custom_feedback = data.get('custom_feedback', '')

    action = "标记" if is_good_case else "取消标记"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" (包含反馈: 标签{len(feedback_tags)}个, 文本{len(custom_feedback)}字符)"
    logger.info(f"用户 {username} {action}会话 {conversation_id} 为Good Case{feedback_info}。")

    try:
        # 检查用户是否有权限操作此会话
        is_owner, conversation_exists = check_conversation_ownership(username, email, conversation_id)
        if not conversation_exists:
            logger.warning(f"会话 {conversation_id} 不存在，用户：{username}")
            return jsonify({'error': '会话不存在。'}), 404
        
        if not is_owner:
            logger.warning(f"用户 {username} 无权{action}会话 {conversation_id} 为Good Case")
            return jsonify({'error': '您无权操作此会话。'}), 403

        success = mark_good_case(conversation_id, is_good_case, user_name=username,
                               feedback_tags=feedback_tags, custom_feedback=custom_feedback)
        if success:
            message = '会话已标记为Good Case。' if is_good_case else '会话已取消标记为Good Case。'
            return jsonify({'status': 'success', 'message': message, 'is_good_case': is_good_case})
        else:
            # 如果会话不存在或用户无权访问
            logger.warning(
                f"无法{action}不存在或未授权的会话 {conversation_id} 为Good Case，用户：{username}")
            return jsonify({
                'error': f'无法{action}会话。该会话可能不存在或不属于此用户。'}), 404
    except Exception as e:
        logger.exception(
            f"用户 {username} {action}会话 {conversation_id} 为Good Case时出错：{e}")
        return jsonify({'error': f'服务器内部错误，无法{action}Good Case。'}), 500


@good_case_bp.route('/api/dashboard/mark_good_case', methods=['POST'])
@login_required
@admin_required
def mark_good_case_dashboard():
    """
    POST /api/dashboard/mark_good_case endpoint that marks a conversation as a good case.
    """
    data = request.get_json()
    if not data or 'conversation_id' not in data:
        return jsonify({'error': 'Missing conversation_id'}), 400

    conversation_id = data['conversation_id']
    is_good_case = data.get('is_good_case', True)
    username = data.get('username')
    if not username:
        return jsonify({'error': 'Missing username in request data'}), 400

    try:
        admin_name = session.get('user_info', {}).get('name', 'Admin')
        success = mark_good_case(conversation_id, is_good_case, user_name=admin_name)
        if success:
            logger.info(
                f"Admin {session.get('user_info', {}).get('name')} marked conversation {conversation_id} for user {username} as good case: {is_good_case}")
            return jsonify({'status': 'success', 'is_good_case': is_good_case})
        else:
            # This could happen if the conversation doesn't exist for the user
            logger.warning(
                f"Admin failed to mark non-existent or unauthorized conversation {conversation_id} as good case for user {username}")
            return jsonify({
                               'error': 'Failed to mark conversation. It might not exist or belong to this user.'}), 404
    except Exception as e:
        logger.error(f"Error marking conversation as good case: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@good_case_bp.route('/api/mark_chat_history_as_good_case', methods=['POST'])
@login_required
def mark_chat_history_as_good_case():
    """
    POST /api/mark_chat_history_as_good_case 端点，将指定的chat_history记录标记为Good Case。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "未在会话中找到用户信息"}), 401

    data = request.get_json()
    if not data or 'chat_history_id' not in data:
        return jsonify({'error': '缺少chat_history_id'}), 400

    chat_history_id = data['chat_history_id']

    # 确保chat_history_id是整数类型
    try:
        chat_history_id = int(chat_history_id)
    except (ValueError, TypeError):
        logger.error(f"无效的chat_history_id格式: {chat_history_id}")
        return jsonify({'error': '无效的消息ID格式'}), 400
    # 获取 is_good_case 参数，默认为 True（向后兼容）
    is_good_case = data.get('is_good_case', True)

    # 获取反馈数据（可选）
    feedback_tags = data.get('feedback_tags', [])
    custom_feedback = data.get('custom_feedback', '')

    action = "标记" if is_good_case else "取消标记"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" (包含反馈: 标签{len(feedback_tags)}个, 文本{len(custom_feedback)}字符)"
    logger.info(f"用户 {username} {action}chat_history {chat_history_id} 为Good Case{feedback_info}。")

    try:
        # 检查用户是否有权限操作此chat_history记录
        is_owner, chat_history_exists = check_chat_history_ownership(username, email, chat_history_id)
        if not chat_history_exists:
            logger.warning(f"Chat history {chat_history_id} 不存在，用户：{username}")
            return jsonify({'error': 'Chat history记录不存在。'}), 404

        if not is_owner:
            logger.warning(f"用户 {username} 无权{action}chat_history {chat_history_id} 为Good Case")
            return jsonify({'error': '您无权操作此消息记录。'}), 403

        success = mark_good_case_by_chat_history_id(chat_history_id, is_good_case, user_name=username,
                                                  feedback_tags=feedback_tags, custom_feedback=custom_feedback)
        if success:
            message = '消息已标记为Good Case。' if is_good_case else '消息已取消标记为Good Case。'
            return jsonify({'status': 'success', 'message': message, 'is_good_case': is_good_case})
        else:
            # 如果chat_history记录不存在或用户无权访问
            logger.warning(
                f"无法{action}不存在或未授权的chat_history {chat_history_id} 为Good Case，用户：{username}")
            return jsonify({
                'error': f'无法{action}消息记录。该记录可能不存在或不属于此用户。'}), 404
    except Exception as e:
        logger.exception(
            f"用户 {username} {action}chat_history {chat_history_id} 为Good Case时出错：{e}")
        return jsonify({'error': f'服务器内部错误，无法{action}Good Case。'}), 500
