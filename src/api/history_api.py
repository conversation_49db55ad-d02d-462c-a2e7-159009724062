"""
管理会话历史的 API 端点。
"""
from flask import request, jsonify, session, Blueprint

from src.services.auth.user_login_with_feishu import login_required
from src.services.chatbot.history_service import (
    get_user_conversations,
    delete_user_conversation,
    check_conversation_ownership,
    get_conversation_messages,
    get_conversation_count,
    check_chat_history_ownership
)
from src.services.chatbot.bad_case_service import mark_bad_case, mark_bad_case_by_chat_history_id
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email

# 创建一个 Blueprint，用于管理会话历史
# 使用 url_prefix='' 保留原有的 URL 路径
history_bp = Blueprint('history', __name__, url_prefix='')


@history_bp.route('/api/history', methods=['GET'])
@login_required
def get_history():
    """
    GET /api/history 端点，获取登录用户的会话历史，支持分页，
    或者通过 chat 参数单独获取某条会话详情（仅限登录用户）。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "用户信息未在 session 中找到"}), 401

    chat = request.args.get('chat')
    if chat:
        # 有 chat 参数，查单条会话内容
        try:
            # 校验此会话是否属于当前用户
            is_owner, is_exists= check_conversation_ownership(username, email, chat)
            if not is_exists:
                return jsonify({"error": "会话不存在"}), 404
            if not is_owner:
                return jsonify({"error": "没有权限访问该会话"}), 400
            messages = get_conversation_messages(chat, username, email)
            return jsonify({
                "conversation": messages
            })
        except Exception as e:
            logger.error(f"加载会话 {chat} 时出错：{e}")
            return jsonify({"error": "加载会话失败"}), 500
    else:
        # 分页获取全部历史
        # 获取分页参数
        try:
            limit = int(request.args.get('limit', 20))  # 默认 20
            offset = int(request.args.get('offset', 0))  # 默认 0
        except ValueError:
            return jsonify({"error": "limit 或 offset 参数无效，必须为整数"}), 400

        if limit <= 0 or offset < 0:
            return jsonify({"error": "limit 必须为正数，offset 必须为非负数"}), 400

        try:
            # 获取当前用户的全部会话数量
            total_count = get_conversation_count(username, email)
            history_data = get_user_conversations(username, email, limit=limit, offset=offset)
            return jsonify({
                "history": history_data,
                "total_count": total_count
            })
        except Exception as e:
            logger.error(f"加载历史数据时出错：{username} (limit={limit}, offset={offset}): {e}")
            return jsonify({"error": "加载历史数据失败"}), 500


@history_bp.route('/api/history/<conversation_id>', methods=['DELETE'])
@login_required
def delete_history_conversation(conversation_id):
    """
    DELETE /api/history/<conversation_id> 端点，删除指定会话历史。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "未在会话中找到用户信息"}), 401
    if not conversation_id:
        return jsonify({"error": "缺少会话ID"}), 400

    try:
        success = delete_user_conversation(username, email, conversation_id)
        if success:
            logger.info(f"用户 {username} 删除会话 {conversation_id}")
            return jsonify({"status": "success", "message": "会话已删除"})
        else:
            # 如果会话不存在或用户无权访问
            logger.warning(
                f"尝试删除不存在的会话 {conversation_id}，用户：{username}")
            return jsonify(
                {"status": "success", "message": "会话不存在或已删除"})
    except Exception as e:
        logger.error(f"用户 {username} 删除会话 {conversation_id} 时出错：{e}")
        return jsonify({"error": "无法删除会话"}), 500


@history_bp.route('/api/mark_conversation_as_bad_case', methods=['POST'])
@login_required
def mark_conversation_as_bad_case():
    """
    POST /api/mark_conversation_as_bad_case 端点，将会话标记为不良案例。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "未在会话中找到用户信息"}), 401

    data = request.get_json()
    if not data or 'conversation_id' not in data:
        return jsonify({'error': '缺少会话ID'}), 400

    conversation_id = data['conversation_id']
    # 获取 is_bad_case 参数，默认为 True（向后兼容）
    is_bad_case = data.get('is_bad_case', True)

    # 获取反馈数据（可选）
    feedback_tags = data.get('feedback_tags', [])
    custom_feedback = data.get('custom_feedback', '')

    action = "标记" if is_bad_case else "取消标记"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" (包含反馈: 标签{len(feedback_tags)}个, 文本{len(custom_feedback)}字符)"
    logger.info(f"用户 {username} {action}会话 {conversation_id} 为不良案例{feedback_info}。")

    try:
        success = mark_bad_case(conversation_id, is_bad_case, user_name=username,
                              feedback_tags=feedback_tags, custom_feedback=custom_feedback)
        if success:
            message = '会话已标记为不良案例。' if is_bad_case else '会话已取消标记为不良案例。'
            return jsonify({'status': 'success', 'message': message, 'is_bad_case': is_bad_case})
        else:
            # 如果会话不存在或用户无权访问
            logger.warning(
                f"无法{action}不存在或未授权的会话 {conversation_id} 为不良案例，用户：{username}")
            return jsonify({
                'error': f'无法{action}会话。该会话可能不存在或不属于此用户。'}), 404
    except Exception as e:
        logger.error(
            f"用户 {username} {action}会话 {conversation_id} 为不良案例时出错：{e}")
        return jsonify({'error': f'服务器内部错误，无法{action}不良案例。'}), 500


@history_bp.route('/api/mark_chat_history_as_bad_case', methods=['POST'])
@login_required
def mark_chat_history_as_bad_case():
    """
    POST /api/mark_chat_history_as_bad_case 端点，将指定的chat_history记录标记为Bad Case。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "未在会话中找到用户信息"}), 401

    data = request.get_json()
    if not data or 'chat_history_id' not in data:
        return jsonify({'error': '缺少chat_history_id'}), 400

    chat_history_id = data['chat_history_id']

    # 处理前端传递的临时ID问题
    # 如果chat_history_id是字符串且以ai-开头，说明是前端生成的临时ID
    if isinstance(chat_history_id, str) and chat_history_id.startswith('ai-'):
        logger.warning(f"接收到前端临时ID: {chat_history_id}，需要根据其他信息查找真实的数据库ID")

        # 尝试从请求中获取conversation_id和其他信息来定位记录
        conversation_id = data.get('conversation_id')
        if not conversation_id:
            logger.error(f"前端传递临时ID {chat_history_id} 但未提供conversation_id")
            return jsonify({'error': '无法定位消息记录，请刷新页面重试'}), 400

        # 根据conversation_id和用户信息查找最近的assistant消息
        try:
            from src.repositories.chatbi.history import execute_db_query

            # 查找该对话中最近的assistant消息
            sql = """
                SELECT id FROM chat_history
                WHERE conversation_id = %s AND username = %s AND email = %s AND role = 'assistant'
                ORDER BY timestamp DESC
                LIMIT 1
            """
            result = execute_db_query(sql, (conversation_id, username, email), fetch='one')

            if result and result.get('id'):
                real_chat_history_id = result['id']
                logger.info(f"成功将前端临时ID {chat_history_id} 映射到数据库ID {real_chat_history_id}")
                chat_history_id = real_chat_history_id
            else:
                logger.error(f"无法找到对应的数据库记录，conversation_id: {conversation_id}, user: {username}")
                return jsonify({'error': '无法找到对应的消息记录，请刷新页面重试'}), 404

        except Exception as e:
            logger.error(f"查找真实chat_history_id时出错: {str(e)}")
            return jsonify({'error': '服务器内部错误，无法定位消息记录'}), 500

    # 确保chat_history_id是整数类型
    try:
        chat_history_id = int(chat_history_id)
    except (ValueError, TypeError):
        logger.error(f"无效的chat_history_id格式: {chat_history_id}")
        return jsonify({'error': '无效的消息ID格式'}), 400
    # 获取 is_bad_case 参数，默认为 True（向后兼容）
    is_bad_case = data.get('is_bad_case', True)

    # 获取反馈数据（可选）
    feedback_tags = data.get('feedback_tags', [])
    custom_feedback = data.get('custom_feedback', '')

    action = "标记" if is_bad_case else "取消标记"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" (包含反馈: 标签{len(feedback_tags)}个, 文本{len(custom_feedback)}字符)"
    logger.info(f"用户 {username} {action}chat_history {chat_history_id} 为不良案例{feedback_info}。")

    try:
        # 检查用户是否有权限操作此chat_history记录
        is_owner, chat_history_exists = check_chat_history_ownership(username, email, chat_history_id)
        if not chat_history_exists:
            logger.warning(f"Chat history {chat_history_id} 不存在，用户：{username}")
            return jsonify({'error': 'Chat history记录不存在。'}), 404

        if not is_owner:
            logger.warning(f"用户 {username} 无权{action}chat_history {chat_history_id} 为不良案例")
            return jsonify({'error': '您无权操作此消息记录。'}), 403

        success = mark_bad_case_by_chat_history_id(chat_history_id, is_bad_case, user_name=username,
                                                 feedback_tags=feedback_tags, custom_feedback=custom_feedback)
        if success:
            message = '消息已标记为不良案例。' if is_bad_case else '消息已取消标记为不良案例。'
            return jsonify({'status': 'success', 'message': message, 'is_bad_case': is_bad_case})
        else:
            # 如果chat_history记录不存在或用户无权访问
            logger.warning(
                f"无法{action}不存在或未授权的chat_history {chat_history_id} 为不良案例，用户：{username}")
            return jsonify({
                'error': f'无法{action}消息记录。该记录可能不存在或不属于此用户。'}), 404
    except Exception as e:
        logger.error(
            f"用户 {username} {action}chat_history {chat_history_id} 为不良案例时出错：{e}")
        return jsonify({'error': f'服务器内部错误，无法{action}不良案例。'}), 500
